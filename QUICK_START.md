# 快速开始指南

## 🚀 5分钟快速上手

### 1. 环境检查
```bash
# 检查Python版本 (需要3.8+)
python --version

# 检查pip
pip --version
```

### 2. 安装依赖
```bash
# 安装Python包
pip install -r requirements.txt

# 安装浏览器 (可能需要较长时间)
playwright install chromium
```

### 3. 运行测试
```bash
# 测试系统功能
python test_crawler.py
```

### 4. 试运行爬虫
```bash
# 试运行模式 (不保存数据)
python run_crawler.py --dry-run --pages 1
```

### 5. 正式爬取
```bash
# 爬取所有网站
python run_crawler.py

# 查看结果
ls data/output/
```

## 📋 常用命令

### 基础命令
```bash
# 查看帮助
python run_crawler.py --help

# 爬取指定网站
python run_crawler.py --sites liepin

# 设置页数
python run_crawler.py --pages 5

# 指定输出格式
python run_crawler.py --format csv
```

### 调试命令
```bash
# 详细输出
python run_crawler.py --verbose

# 有头浏览器模式
python run_crawler.py --no-headless

# 试运行模式
python run_crawler.py --dry-run
```

## ⚙️ 配置修改

### 修改搜索关键词
编辑 `config/crawler_config.yaml`:
```yaml
websites:
  liepin:
    search_url: "https://www.liepin.com/zhaopin/?key=你的关键词"
  job51:
    search_url: "https://search.51job.com/list/000000,000000,0000,00,9,99,你的关键词,2,1.html"
```

### 调整爬取频率
```yaml
crawler:
  request_delay: 3.0  # 增加到3秒间隔
  
anti_detection:
  random_delay_range: [2, 5]  # 随机2-5秒延迟
```

## 🔧 故障排除

### 问题1: Playwright安装失败
```bash
# 解决方案1: 使用国内镜像
pip install playwright -i https://pypi.tuna.tsinghua.edu.cn/simple/
playwright install chromium

# 解决方案2: 手动下载
export PLAYWRIGHT_DOWNLOAD_HOST=https://playwright.download.prss.microsoft.com
playwright install chromium
```

### 问题2: 爬取失败
```bash
# 检查网络连接
ping www.liepin.com

# 使用调试模式
python run_crawler.py --no-headless --verbose

# 检查配置文件
python test_crawler.py
```

### 问题3: 数据为空
1. 检查网站是否可访问
2. 确认提取模式是否正确
3. 查看日志文件: `logs/crawler_*.log`

## 📊 输出文件说明

### 数据文件
- `data/output/jobs_*.json` - JSON格式数据
- `data/output/jobs_*.csv` - CSV格式数据  
- `data/output/jobs_*.xlsx` - Excel格式数据
- `data/jobs.db` - SQLite数据库文件

### 日志文件
- `logs/crawler_*.log` - 爬虫运行日志
- `logs/crawler_error_*.log` - 错误日志
- `logs/data_processing_*.log` - 数据处理日志

## 🎯 使用技巧

### 1. 批量爬取
```bash
# 创建脚本文件 batch_crawl.sh
#!/bin/bash
for site in liepin job51 job58; do
    echo "爬取 $site..."
    python run_crawler.py --sites $site --pages 10
    sleep 60  # 等待1分钟
done
```

### 2. 定时任务
```bash
# 添加到crontab
# 每天早上8点爬取
0 8 * * * cd /path/to/crawler && python run_crawler.py
```

### 3. 数据分析
```python
import pandas as pd
import json

# 读取JSON数据
with open('data/output/jobs_20240115_103000.json', 'r') as f:
    jobs = json.load(f)

df = pd.DataFrame(jobs)

# 基础统计
print(f"总职位数: {len(df)}")
print(f"公司数量: {df['company'].nunique()}")
print(f"地区分布:\n{df['location'].value_counts().head()}")
```

## 📞 获取帮助

### 查看文档
- `README.md` - 完整文档
- `PROJECT_SUMMARY.md` - 项目总结
- `config/crawler_config.yaml` - 配置说明

### 运行测试
```bash
# 系统功能测试
python test_crawler.py

# 配置验证
python -c "from src.utils import config; print('配置加载成功')"
```

### 常见错误代码
- `ImportError` - 依赖包未安装
- `FileNotFoundError` - 配置文件缺失
- `ConnectionError` - 网络连接问题
- `TimeoutError` - 请求超时

## 🎉 成功标志

当你看到以下输出时，说明系统运行正常：

```
============================================================
爬取统计报告
============================================================
总爬取时间: 45.23 秒
总职位数量: 156 条

各网站爬取结果:
  liepin: 52 条
  job51: 48 条
  job58: 56 条
============================================================
```

## 下一步

1. **自定义配置**: 根据需求修改配置文件
2. **扩展网站**: 添加更多招聘网站
3. **数据分析**: 使用pandas分析爬取的数据
4. **自动化**: 设置定时任务自动爬取

祝你使用愉快！🎊
