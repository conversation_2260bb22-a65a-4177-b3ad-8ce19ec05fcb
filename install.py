#!/usr/bin/env python3
"""
爬虫系统安装脚本
Crawler system installation script
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"正在执行: {description or command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_pip():
    """检查pip是否可用"""
    print("检查pip...")
    try:
        import pip
        print("✅ pip可用")
        return True
    except ImportError:
        print("❌ pip不可用，请先安装pip")
        return False


def install_requirements():
    """安装Python依赖"""
    print("安装Python依赖包...")
    
    # 检查requirements.txt是否存在
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    # 安装依赖
    commands = [
        "pip install --upgrade pip",
        "pip install -r requirements.txt"
    ]
    
    for cmd in commands:
        if not run_command(cmd, f"执行: {cmd}"):
            print("❌ 依赖安装失败")
            return False
    
    print("✅ Python依赖安装完成")
    return True


def install_playwright():
    """安装Playwright浏览器"""
    print("安装Playwright浏览器...")
    
    commands = [
        "playwright install",
        "playwright install-deps"  # 安装系统依赖
    ]
    
    for cmd in commands:
        if not run_command(cmd, f"执行: {cmd}"):
            print("⚠️ Playwright安装可能有问题，但可以继续")
            break
    
    print("✅ Playwright安装完成")
    return True


def create_directories():
    """创建必要的目录"""
    print("创建项目目录...")
    
    directories = [
        "data/output",
        "logs",
        "config/extraction_schemas"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    return True


def check_config_files():
    """检查配置文件"""
    print("检查配置文件...")
    
    config_files = [
        "config/crawler_config.yaml",
        "config/extraction_schemas/liepin_schema.json",
        "config/extraction_schemas/job51_schema.json", 
        "config/extraction_schemas/job58_schema.json"
    ]
    
    missing_files = []
    for config_file in config_files:
        if not Path(config_file).exists():
            missing_files.append(config_file)
        else:
            print(f"✅ 配置文件存在: {config_file}")
    
    if missing_files:
        print("❌ 缺少配置文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    return True


def test_installation():
    """测试安装是否成功"""
    print("测试安装...")
    
    try:
        # 测试导入主要模块
        sys.path.insert(0, str(Path.cwd()))
        
        from src.utils import config, logger
        from src.crawlers import LiepinCrawler
        from src.data_processing import DataCleaner
        
        print("✅ 模块导入测试成功")
        
        # 测试配置加载
        enabled_sites = config.get_enabled_websites()
        print(f"✅ 配置加载成功，启用网站: {len(enabled_sites)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 安装测试失败: {e}")
        return False


def print_usage_info():
    """打印使用说明"""
    print("\n" + "="*60)
    print("🎉 安装完成！")
    print("="*60)
    print("\n使用方法:")
    print("1. 运行测试:")
    print("   python test_crawler.py")
    print("\n2. 试运行爬虫:")
    print("   python run_crawler.py --dry-run")
    print("\n3. 爬取数据:")
    print("   python run_crawler.py")
    print("\n4. 查看帮助:")
    print("   python run_crawler.py --help")
    print("\n5. 爬取指定网站:")
    print("   python run_crawler.py --sites liepin")
    print("\n6. 指定输出格式:")
    print("   python run_crawler.py --format csv")
    
    print("\n配置文件:")
    print("- 主配置: config/crawler_config.yaml")
    print("- 提取模式: config/extraction_schemas/")
    
    print("\n输出目录:")
    print("- 数据文件: data/output/")
    print("- 日志文件: logs/")
    
    print("\n注意事项:")
    print("- 请遵守网站robots.txt协议")
    print("- 合理设置爬取频率，避免对网站造成压力")
    print("- 爬取的数据仅供学习研究使用")
    print("="*60)


def main():
    """主安装函数"""
    print("="*60)
    print("工业职位需求爬虫系统 - 安装程序")
    print("="*60)
    
    # 检查系统环境
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # 创建目录
    if not create_directories():
        sys.exit(1)
    
    # 检查配置文件
    if not check_config_files():
        print("⚠️ 配置文件缺失，但可以继续安装")
    
    # 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 安装Playwright
    if not install_playwright():
        print("⚠️ Playwright安装可能有问题")
    
    # 测试安装
    if not test_installation():
        print("❌ 安装测试失败")
        sys.exit(1)
    
    # 打印使用说明
    print_usage_info()


if __name__ == "__main__":
    main()
