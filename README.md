# 工业职位需求爬虫系统

基于crawl4ai的计算机科学与技术专业职位需求爬虫系统，专门从主流招聘网站爬取相关岗位信息。

## 功能特性

- 🎯 **专业定向**: 专门针对计算机科学与技术专业相关岗位
- 🌐 **多网站支持**: 支持猎聘网、前程无忧、58同城三大招聘平台
- 🚀 **高性能爬取**: 基于crawl4ai异步爬虫框架，支持并发爬取
- 🛡️ **反反爬虫**: 内置用户代理轮换、请求间隔、代理支持等反检测机制
- 🧹 **智能清洗**: 自动数据清洗、去重和格式标准化
- 💾 **多格式存储**: 支持JSON、CSV、Excel和SQLite数据库存储
- 📊 **详细统计**: 提供完整的爬取统计和错误报告
- ⚙️ **灵活配置**: 支持YAML配置文件和命令行参数

## 项目结构

```
Industrial_Position_Demand_Crawler/
├── src/                          # 源代码目录
│   ├── crawlers/                 # 爬虫模块
│   │   ├── base_crawler.py       # 基础爬虫类
│   │   ├── liepin_crawler.py     # 猎聘网爬虫
│   │   ├── job51_crawler.py      # 前程无忧爬虫
│   │   └── job58_crawler.py      # 58同城爬虫
│   ├── data_processing/          # 数据处理模块
│   │   ├── data_cleaner.py       # 数据清洗器
│   │   └── data_storage.py       # 数据存储器
│   ├── utils/                    # 工具模块
│   │   ├── config.py             # 配置管理
│   │   ├── logger.py             # 日志记录
│   │   └── anti_detection.py     # 反反爬虫
│   └── main.py                   # 主程序
├── config/                       # 配置文件目录
│   ├── crawler_config.yaml       # 主配置文件
│   └── extraction_schemas/       # 数据提取模式
│       ├── liepin_schema.json    # 猎聘网提取模式
│       ├── job51_schema.json     # 前程无忧提取模式
│       └── job58_schema.json     # 58同城提取模式
├── data/                         # 数据目录
│   └── output/                   # 输出文件目录
├── logs/                         # 日志目录
├── requirements.txt              # 依赖包列表
├── run_crawler.py               # 启动脚本
└── README.md                    # 项目说明
```

## 安装说明

### 1. 环境要求

- Python 3.8+
- 操作系统: Windows/macOS/Linux

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd Industrial_Position_Demand_Crawler

# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install
```

### 3. 配置设置

编辑 `config/crawler_config.yaml` 文件，根据需要调整配置：

```yaml
# 爬虫基础设置
crawler:
  max_concurrent_requests: 3
  request_delay: 2.0
  headless: true

# 数据存储设置
data_storage:
  output_format: "json"  # json, csv, database
  output_directory: "data/output"
```

## 使用方法

### 基本使用

```bash
# 爬取所有启用的网站
python run_crawler.py

# 爬取指定网站
python run_crawler.py --sites liepin job51

# 设置爬取页数
python run_crawler.py --pages 10

# 指定输出格式
python run_crawler.py --format csv

# 试运行（不保存数据）
python run_crawler.py --dry-run
```

### 高级使用

```bash
# 使用自定义配置文件
python run_crawler.py --config custom_config.yaml

# 指定输出目录
python run_crawler.py --output-dir /path/to/output

# 详细输出模式
python run_crawler.py --verbose

# 使用有头浏览器（调试模式）
python run_crawler.py --no-headless
```

### 编程接口

```python
import asyncio
from src.main import CrawlerManager

async def main():
    # 创建爬虫管理器
    crawler_manager = CrawlerManager()
    
    # 爬取所有网站
    jobs = await crawler_manager.crawl_all_sites(max_pages=5)
    
    # 保存数据
    filepath = crawler_manager.save_jobs(jobs, "json")
    print(f"数据已保存到: {filepath}")
    
    # 获取统计信息
    stats = crawler_manager.get_statistics()
    print(f"总共爬取 {stats['total_jobs']} 条职位")

if __name__ == "__main__":
    asyncio.run(main())
```

## 数据字段说明

爬取的职位数据包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| title | str | 职位名称 |
| company | str | 公司名称 |
| location | str | 工作地点 |
| salary | str | 薪资范围 |
| education | str | 学历要求 |
| experience | str | 工作经验要求 |
| description | str | 职位描述 |
| requirements | str | 职位要求 |
| benefits | str | 福利待遇 |
| source_website | str | 来源网站 |
| source_url | str | 原始链接 |
| crawl_time | datetime | 爬取时间 |
| job_id | str | 职位唯一标识 |

## 配置说明

### 主配置文件 (crawler_config.yaml)

```yaml
# 爬虫设置
crawler:
  max_concurrent_requests: 3    # 最大并发请求数
  request_delay: 2.0           # 请求间隔(秒)
  timeout: 30                  # 请求超时时间
  retry_attempts: 3            # 重试次数
  headless: true              # 无头浏览器模式

# 反反爬虫设置
anti_detection:
  rotate_user_agents: true     # 轮换用户代理
  use_proxy: false            # 使用代理
  random_delay_range: [1, 3]  # 随机延迟范围

# 数据存储设置
data_storage:
  output_format: "json"       # 输出格式
  output_directory: "data/output"  # 输出目录
  enable_deduplication: true  # 启用去重

# 网站配置
websites:
  liepin:
    enabled: true
    search_url: "https://www.liepin.com/zhaopin/?key=计算机科学与技术"
  # ... 其他网站配置
```

### 数据提取模式

每个网站都有对应的JSON提取模式文件，定义了如何从页面中提取职位信息：

```json
{
  "name": "网站名称",
  "baseSelector": ".job-list-item",
  "fields": [
    {
      "name": "title",
      "selector": ".job-title a",
      "type": "text",
      "required": true
    }
    // ... 其他字段定义
  ]
}
```

## 注意事项

### 1. 遵守robots.txt协议

本爬虫系统默认遵守网站的robots.txt协议，请确保您的爬取行为符合网站的使用条款。

### 2. 合理的爬取频率

- 默认请求间隔为2秒，避免对目标网站造成过大压力
- 支持随机延迟，模拟人工访问行为
- 建议在非高峰时段进行大量爬取

### 3. 数据使用规范

- 爬取的数据仅供学习和研究使用
- 请勿用于商业用途或恶意竞争
- 尊重数据版权和隐私保护

### 4. 错误处理

- 系统具备完善的错误处理和重试机制
- 所有错误都会记录到日志文件中
- 支持断点续爬和增量更新

## 故障排除

### 常见问题

1. **安装依赖失败**
   ```bash
   # 使用国内镜像源
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **浏览器启动失败**
   ```bash
   # 重新安装Playwright浏览器
   playwright install --force
   ```

3. **爬取失败或数据为空**
   - 检查网络连接
   - 确认目标网站是否可访问
   - 查看日志文件了解具体错误信息
   - 尝试使用有头浏览器模式调试

4. **数据保存失败**
   - 检查输出目录权限
   - 确认磁盘空间充足
   - 检查数据库连接配置

### 日志文件

系统会在 `logs/` 目录下生成详细的日志文件：

- `crawler_YYYY-MM-DD.log`: 爬虫运行日志
- `crawler_error_YYYY-MM-DD.log`: 错误日志
- `data_processing_YYYY-MM-DD.log`: 数据处理日志
- `storage_YYYY-MM-DD.log`: 数据存储日志

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd Industrial_Position_Demand_Crawler

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements.txt
pip install pytest black flake8  # 开发工具
```

### 代码规范

- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 添加适当的类型注解
- 编写单元测试

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**免责声明**: 本工具仅供学习和研究使用，使用者需自行承担使用风险，并遵守相关法律法规和网站使用条款。
