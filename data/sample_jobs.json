[{"job_id": "sample001", "title": "Python后端开发工程师", "company": "某某科技有限公司", "location": "北京-朝阳区", "salary": "15K-25K", "education": "本科", "experience": "3-5年", "description": "负责公司核心业务系统的后端开发，使用Python/Django框架，参与系统架构设计和优化。", "requirements": "1. 计算机相关专业本科以上学历；2. 3年以上Python开发经验；3. 熟悉Django/Flask框架；4. 熟悉MySQL、Redis等数据库；5. 有分布式系统开发经验优先。", "benefits": "五险一金，弹性工作，年终奖，股票期权", "source_website": "liepin", "source_url": "https://www.liepin.com/job/sample001", "crawl_time": "2024-01-15T10:30:00"}, {"job_id": "sample002", "title": "前端开发工程师", "company": "互联网科技公司", "location": "上海-浦东新区", "salary": "12K-20K", "education": "本科", "experience": "2-4年", "description": "负责公司Web前端和移动端H5页面开发，与产品、设计团队协作完成项目需求。", "requirements": "1. 计算机相关专业；2. 熟练掌握HTML5、CSS3、JavaScript；3. 熟悉Vue.js或React框架；4. 有移动端开发经验；5. 良好的代码规范和团队协作能力。", "benefits": "六险一金，免费午餐，健身房，培训机会", "source_website": "job51", "source_url": "https://jobs.51job.com/sample002", "crawl_time": "2024-01-15T10:35:00"}, {"job_id": "sample003", "title": "Java开发工程师", "company": "金融科技公司", "location": "深圳-南山区", "salary": "18K-30K", "education": "本科", "experience": "3-6年", "description": "参与金融业务系统开发，负责高并发、高可用系统的设计和实现。", "requirements": "1. 计算机科学与技术等相关专业；2. 熟练掌握Java编程；3. 熟悉Spring Boot、MyBatis等框架；4. 有微服务架构经验；5. 了解金融业务优先。", "benefits": "高薪酬，股票激励，带薪年假，技术培训", "source_website": "job58", "source_url": "https://www.58.com/job/sample003", "crawl_time": "2024-01-15T10:40:00"}, {"job_id": "sample004", "title": "算法工程师", "company": "人工智能公司", "location": "杭州-西湖区", "salary": "25K-40K", "education": "硕士", "experience": "3-5年", "description": "负责机器学习算法研发，参与推荐系统、自然语言处理等项目。", "requirements": "1. 计算机、数学、统计学等相关专业硕士以上；2. 熟悉机器学习、深度学习算法；3. 熟练使用Python、TensorFlow/PyTorch；4. 有实际项目经验；5. 良好的数学基础。", "benefits": "期权激励，弹性工作，技术氛围，成长空间", "source_website": "liepin", "source_url": "https://www.liepin.com/job/sample004", "crawl_time": "2024-01-15T10:45:00"}, {"job_id": "sample005", "title": "DevOps工程师", "company": "云计算公司", "location": "成都-高新区", "salary": "16K-28K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流水线建设，容器化部署，监控告警系统维护。", "requirements": "1. 计算机相关专业本科以上；2. 熟悉Linux系统管理；3. 熟练使用Docker、Kubernetes；4. 熟悉Jenkins、GitLab CI等工具；5. 有云平台使用经验。", "benefits": "弹性工作，远程办公，技术津贴，年度旅游", "source_website": "job51", "source_url": "https://jobs.51job.com/sample005", "crawl_time": "2024-01-15T10:50:00"}]