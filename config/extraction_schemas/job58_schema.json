{"name": "58Job <PERSON>s Extraction Schema", "description": "58同城职位信息提取模式", "baseSelector": ".job-list .job-item, .list .item, .job-card, .job_item", "fields": [{"name": "title", "selector": ".job-title a, .job-name a, .title a, .job_name a", "type": "text", "required": true}, {"name": "company", "selector": ".company-name a, .comp-name, .company, .comp_name", "type": "text", "required": true}, {"name": "location", "selector": ".job-area, .address, .location, .job_area", "type": "text", "required": true}, {"name": "salary", "selector": ".job-salary, .salary, .price, .job_salary", "type": "text", "required": true}, {"name": "experience", "selector": ".job-require, .require, .experience, .job_require", "type": "text", "required": false}, {"name": "education", "selector": ".job-require, .require, .education, .job_require", "type": "text", "required": false}, {"name": "job_url", "selector": ".job-title a, .job-name a, .title a, .job_name a", "type": "attribute", "attribute": "href", "required": false}, {"name": "company_url", "selector": ".company-name a, .comp-name a, .comp_name a", "type": "attribute", "attribute": "href", "required": false}, {"name": "publish_time", "selector": ".job-time, .time, .date, .job_time", "type": "text", "required": false}, {"name": "job_type", "selector": ".job-type, .type, .job_type", "type": "text", "required": false}, {"name": "contact_info", "selector": ".contact-info, .contact, .contact_info", "type": "text", "required": false}, {"name": "welfare", "selector": ".welfare, .benefits, .job_welfare", "type": "text", "required": false}], "pagination": {"next_page_selector": ".pager .next, .pagination .next", "page_number_selector": ".pager .current, .pagination .current"}, "filters": {"min_title_length": 2, "min_company_length": 2, "required_fields": ["title", "company", "location", "salary"], "exclude_keywords": ["兼职", "小时工", "临时工", "代理", "刷单", "打字员"]}}