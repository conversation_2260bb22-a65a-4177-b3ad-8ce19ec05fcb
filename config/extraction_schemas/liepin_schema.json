{"name": "<PERSON><PERSON> Jobs Extraction Schema", "description": "猎聘网职位信息提取模式", "baseSelector": ".job-list-item, .job-card-box, [data-selector='job-list-item'], .sojob-item-main", "fields": [{"name": "title", "selector": ".job-title a, .job-name a, h3 a, .ellipsis-1", "type": "text", "required": true}, {"name": "company", "selector": ".company-name a, .comp-name a, .company-info a, .company-name", "type": "text", "required": true}, {"name": "location", "selector": ".job-area, .work-addr, .job-location, .area", "type": "text", "required": true}, {"name": "salary", "selector": ".text-warning, .job-salary, .salary, .condition", "type": "text", "required": true}, {"name": "experience", "selector": ".job-require .text-muted, .job-tags .tag, .experience, .condition", "type": "text", "required": false}, {"name": "education", "selector": ".job-require .text-muted, .job-tags .tag, .education, .condition", "type": "text", "required": false}, {"name": "job_url", "selector": ".job-title a, .job-name a, h3 a", "type": "attribute", "attribute": "href", "required": false}, {"name": "company_url", "selector": ".company-name a, .comp-name a", "type": "attribute", "attribute": "href", "required": false}, {"name": "description", "selector": ".job-content, .job-desc, .job-description, .job-detail", "type": "text", "required": false}, {"name": "publish_time", "selector": ".time-info, .job-time, .time", "type": "text", "required": false}, {"name": "company_size", "selector": ".company-scale, .company-info .scale", "type": "text", "required": false}, {"name": "company_industry", "selector": ".company-industry, .company-info .industry", "type": "text", "required": false}], "pagination": {"next_page_selector": ".pager .next, .pagination .next", "page_number_selector": ".pager .current, .pagination .current"}, "filters": {"min_title_length": 2, "min_company_length": 2, "required_fields": ["title", "company", "location", "salary"]}}