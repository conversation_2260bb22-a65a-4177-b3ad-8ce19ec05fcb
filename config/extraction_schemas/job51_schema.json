{"name": "51Job <PERSON>s Extraction Schema", "description": "前程无忧职位信息提取模式", "baseSelector": ".j_joblist .e, .joblist-item, .job-item, .j_result", "fields": [{"name": "title", "selector": ".t1 a, .job-title a, .jobname a, .jname a", "type": "text", "required": true}, {"name": "company", "selector": ".t2 a, .company-name a, .cname a, .cname", "type": "text", "required": true}, {"name": "location", "selector": ".t3, .job-area, .area, .workarea", "type": "text", "required": true}, {"name": "salary", "selector": ".t4, .job-salary, .sal, .money", "type": "text", "required": true}, {"name": "experience", "selector": ".t5, .job-require, .require, .info", "type": "text", "required": false}, {"name": "education", "selector": ".t5, .job-require, .require, .info", "type": "text", "required": false}, {"name": "job_url", "selector": ".t1 a, .job-title a, .jobname a, .jname a", "type": "attribute", "attribute": "href", "required": false}, {"name": "company_url", "selector": ".t2 a, .company-name a, .cname a", "type": "attribute", "attribute": "href", "required": false}, {"name": "publish_time", "selector": ".t6, .job-time, .time, .uptime", "type": "text", "required": false}, {"name": "job_type", "selector": ".job-type, .type, .jobtype", "type": "text", "required": false}, {"name": "company_type", "selector": ".company-type, .ctype", "type": "text", "required": false}, {"name": "company_size", "selector": ".company-size, .csize", "type": "text", "required": false}], "pagination": {"next_page_selector": ".pn .next, .pagination .next", "page_number_selector": ".pn .on, .pagination .current"}, "filters": {"min_title_length": 2, "min_company_length": 2, "required_fields": ["title", "company", "location", "salary"]}}