#!/usr/bin/env python3
"""
爬虫系统启动脚本
Crawler system startup script
"""

import sys
import asyncio
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.main import CrawlerManager
from src.utils import logger, config


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="工业职位需求爬虫系统 - Industrial Position Demand Crawler",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_crawler.py                    # 爬取所有启用的网站
  python run_crawler.py --sites liepin     # 只爬取猎聘网
  python run_crawler.py --pages 10         # 每个网站爬取10页
  python run_crawler.py --format csv       # 输出CSV格式
  python run_crawler.py --config custom.yaml  # 使用自定义配置文件
        """
    )
    
    parser.add_argument(
        '--sites',
        nargs='+',
        choices=['liepin', 'job51', 'job58'],
        help='指定要爬取的网站 (默认: 所有启用的网站)'
    )
    
    parser.add_argument(
        '--pages',
        type=int,
        default=None,
        help='每个网站爬取的最大页数 (默认: 配置文件中的设置)'
    )
    
    parser.add_argument(
        '--format',
        choices=['json', 'csv', 'database'],
        help='输出格式 (默认: 配置文件中的设置)'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='配置文件路径 (默认: config/crawler_config.yaml)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        help='输出目录 (默认: data/output)'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='使用无头浏览器模式'
    )
    
    parser.add_argument(
        '--no-headless',
        action='store_true',
        help='使用有头浏览器模式（用于调试）'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式（不保存数据）'
    )
    
    return parser.parse_args()


async def run_crawler_with_args(args):
    """根据命令行参数运行爬虫"""
    
    # 更新配置
    if args.config:
        # 重新加载配置文件
        from src.utils.config import Config
        global config
        config = Config(args.config)
    
    if args.output_dir:
        config.data_storage.output_directory = args.output_dir
    
    if args.format:
        config.data_storage.output_format = args.format
    
    if args.headless:
        config.crawler.headless = True
    elif args.no_headless:
        config.crawler.headless = False
    
    # 创建输出目录
    Path(config.data_storage.output_directory).mkdir(parents=True, exist_ok=True)
    Path("logs").mkdir(parents=True, exist_ok=True)
    
    # 创建爬虫管理器
    crawler_manager = CrawlerManager()
    
    try:
        if args.sites:
            # 爬取指定网站
            all_jobs = []
            for site_name in args.sites:
                if site_name in crawler_manager.crawlers:
                    logger.info(f"爬取指定网站: {site_name}")
                    jobs = await crawler_manager.crawl_single_site(site_name, args.pages)
                    all_jobs.extend(jobs)
                else:
                    logger.warning(f"网站 {site_name} 未启用或不存在")
        else:
            # 爬取所有网站
            all_jobs = await crawler_manager.crawl_all_sites(args.pages)
        
        if all_jobs and not args.dry_run:
            # 保存数据
            output_format = args.format or config.data_storage.output_format
            filepath = crawler_manager.save_jobs(all_jobs, output_format)
            
            # 同时保存JSON格式（便于后续处理）
            if output_format != "json":
                crawler_manager.save_jobs(all_jobs, "json")
            
            # 导出Excel文件
            try:
                excel_path = crawler_manager.data_storage.export_to_excel(all_jobs)
                logger.info(f"Excel文件已导出: {excel_path}")
            except Exception as e:
                logger.warning(f"导出Excel文件失败: {e}")
        
        elif args.dry_run:
            logger.info(f"试运行完成，共获取 {len(all_jobs)} 条职位数据（未保存）")
        
        # 打印统计信息
        if args.verbose or not args.dry_run:
            crawler_manager.print_statistics()
        
        return len(all_jobs)
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        return 0
    except Exception as e:
        logger.error(f"爬取过程发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return -1


def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志级别
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("启动工业职位需求爬虫系统")
    logger.info(f"配置文件: {args.config or 'config/crawler_config.yaml'}")
    logger.info(f"输出格式: {args.format or config.data_storage.output_format}")
    logger.info(f"输出目录: {args.output_dir or config.data_storage.output_directory}")
    
    if args.sites:
        logger.info(f"指定网站: {', '.join(args.sites)}")
    
    if args.pages:
        logger.info(f"最大页数: {args.pages}")
    
    if args.dry_run:
        logger.info("试运行模式：不会保存数据")
    
    # 运行爬虫
    try:
        result = asyncio.run(run_crawler_with_args(args))
        
        if result > 0:
            logger.info(f"爬取完成，共获取 {result} 条职位数据")
            sys.exit(0)
        elif result == 0:
            logger.info("爬取完成，未获取到数据")
            sys.exit(0)
        else:
            logger.error("爬取失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"系统错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
