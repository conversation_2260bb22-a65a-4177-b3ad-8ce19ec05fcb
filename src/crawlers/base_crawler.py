"""
基础爬虫类
Base crawler class
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pathlib import Path

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from ..utils import crawler_logger, config, anti_detection
from ..data_processing import DataCleaner, JobPosition


class BaseCrawler(ABC):
    """基础爬虫类"""
    
    def __init__(self, website_name: str):
        self.website_name = website_name
        self.website_config = config.get_website_config(website_name)
        self.data_cleaner = DataCleaner()
        
        # 加载提取模式
        self.extraction_schema = self._load_extraction_schema()
        
        # 爬虫配置
        self.browser_config = self._get_browser_config()
        self.crawler_config = self._get_crawler_config()
    
    def _load_extraction_schema(self) -> Dict[str, Any]:
        """加载数据提取模式"""
        schema_path = Path(self.website_config.extraction_schema_path)
        
        if not schema_path.exists():
            crawler_logger.warning(f"提取模式文件不存在: {schema_path}")
            return self._get_default_schema()
        
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            crawler_logger.info(f"加载提取模式: {schema_path}")
            return schema
        except Exception as e:
            crawler_logger.error(f"加载提取模式失败: {e}")
            return self._get_default_schema()
    
    @abstractmethod
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        pass
    
    def _get_browser_config(self) -> BrowserConfig:
        """获取浏览器配置"""
        # 检查是否使用代理
        use_proxy, proxy_url = anti_detection.should_use_proxy(
            config.anti_detection.proxy_list,
            config.anti_detection.use_proxy
        )
        
        return anti_detection.get_browser_config(
            headless=config.crawler.headless,
            use_proxy=use_proxy,
            proxy_url=proxy_url
        )
    
    def _get_crawler_config(self) -> CrawlerRunConfig:
        """获取爬虫运行配置"""
        return CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(self.extraction_schema),
            js_code=anti_detection.get_js_stealth_code(),
            wait_for_images=False,
            process_iframes=False,
            remove_overlay_elements=True,
            verbose=True
        )
    
    async def crawl_search_page(self, search_url: str, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面"""
        url = self._build_search_url(search_url, page)
        
        crawler_logger.log_crawl_start(self.website_name, url)
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # 添加随机延迟
                await anti_detection.random_delay(
                    *config.anti_detection.random_delay_range
                )
                
                # 执行爬取
                result = await crawler.arun(url=url, config=self.crawler_config)
                
                if not result.success:
                    crawler_logger.log_crawl_error(
                        self.website_name, url, result.error_message
                    )
                    return []
                
                # 解析提取的数据
                extracted_data = self._parse_extracted_data(result.extracted_content)
                
                crawler_logger.log_crawl_success(
                    self.website_name, url, len(extracted_data)
                )
                
                return extracted_data
                
        except Exception as e:
            crawler_logger.log_crawl_error(self.website_name, url, str(e))
            return []
    
    def _parse_extracted_data(self, extracted_content: str) -> List[Dict[str, Any]]:
        """解析提取的数据"""
        try:
            if not extracted_content:
                return []
            
            data = json.loads(extracted_content)
            
            # 如果是列表，直接返回
            if isinstance(data, list):
                return data
            
            # 如果是字典，尝试获取数据列表
            if isinstance(data, dict):
                # 常见的数据字段名
                for key in ['data', 'items', 'results', 'jobs', 'list']:
                    if key in data and isinstance(data[key], list):
                        return data[key]
                
                # 如果没有找到列表字段，将字典包装为列表
                return [data]
            
            return []
            
        except json.JSONDecodeError as e:
            crawler_logger.error(f"解析JSON数据失败: {e}")
            return []
        except Exception as e:
            crawler_logger.error(f"处理提取数据失败: {e}")
            return []
    
    @abstractmethod
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        pass
    
    @abstractmethod
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        pass
    
    async def crawl_multiple_pages(self, max_pages: int = 5) -> List[JobPosition]:
        """爬取多页数据"""
        all_jobs = []
        
        for page in range(1, max_pages + 1):
            crawler_logger.info(f"爬取第 {page} 页数据")
            
            try:
                # 爬取搜索页面
                jobs_data = await self.crawl_search_page(
                    self.website_config.search_url, page
                )
                
                if not jobs_data:
                    crawler_logger.warning(f"第 {page} 页没有获取到数据")
                    continue
                
                # 清洗数据
                cleaned_jobs = self.data_cleaner.clean_job_list(
                    jobs_data, 
                    self.website_name,
                    self.website_config.search_url
                )
                
                all_jobs.extend(cleaned_jobs)
                
                # 添加页面间延迟
                if page < max_pages:
                    await anti_detection.random_delay(
                        config.crawler.request_delay,
                        config.crawler.request_delay + 2
                    )
                
            except Exception as e:
                crawler_logger.error(f"爬取第 {page} 页失败: {e}")
                continue
        
        crawler_logger.info(f"完成爬取，共获取 {len(all_jobs)} 条有效职位数据")
        return all_jobs
    
    async def crawl_with_retry(self, url: str, max_retries: int = None) -> Optional[Dict[str, Any]]:
        """带重试的爬取"""
        max_retries = max_retries or config.crawler.retry_attempts
        
        for attempt in range(max_retries):
            try:
                async with AsyncWebCrawler(config=self.browser_config) as crawler:
                    result = await crawler.arun(url=url, config=self.crawler_config)
                    
                    if result.success:
                        return self._parse_extracted_data(result.extracted_content)
                    else:
                        crawler_logger.warning(
                            f"爬取失败 (尝试 {attempt + 1}/{max_retries}): {result.error_message}"
                        )
                        
            except Exception as e:
                crawler_logger.warning(
                    f"爬取异常 (尝试 {attempt + 1}/{max_retries}): {e}"
                )
            
            # 重试前等待
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        crawler_logger.error(f"爬取最终失败: {url}")
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': self.website_name,
            'base_url': self.website_config.base_url,
            'search_url': self.website_config.search_url,
            'enabled': self.website_config.enabled,
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
