"""
反反爬虫机制模块
Anti-detection mechanisms module
"""

import random
import asyncio
from typing import List, Dict, Any
from fake_useragent import UserAgent
from crawl4ai.async_configs import BrowserConfig


class AntiDetectionManager:
    """反反爬虫管理器"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.user_agents = self._get_user_agents()
        self.current_ua_index = 0
    
    def _get_user_agents(self) -> List[str]:
        """获取用户代理列表"""
        user_agents = [
            # Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            
            # Firefox
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
            
            # Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            
            # Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        ]
        return user_agents
    
    def get_random_user_agent(self) -> str:
        """获取随机用户代理"""
        try:
            return self.ua.random
        except:
            return random.choice(self.user_agents)
    
    def get_rotating_user_agent(self) -> str:
        """获取轮换用户代理"""
        ua = self.user_agents[self.current_ua_index]
        self.current_ua_index = (self.current_ua_index + 1) % len(self.user_agents)
        return ua
    
    def get_browser_config(self, 
                          headless: bool = True,
                          use_proxy: bool = False,
                          proxy_url: str = None) -> BrowserConfig:
        """获取浏览器配置"""
        
        # 基础配置
        config_params = {
            "headless": headless,
            "java_script_enabled": True,
            "accept_downloads": False,
            "ignore_https_errors": True,
        }
        
        # 添加代理配置
        if use_proxy and proxy_url:
            config_params["proxy"] = proxy_url
        
        return BrowserConfig(**config_params)
    
    def get_headers(self, website: str = None) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "User-Agent": self.get_rotating_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "max-age=0",
        }
        
        # 根据网站添加特定头部
        if website == "liepin":
            headers.update({
                "Referer": "https://www.liepin.com/",
                "Origin": "https://www.liepin.com"
            })
        elif website == "job51":
            headers.update({
                "Referer": "https://www.51job.com/",
                "Origin": "https://www.51job.com"
            })
        elif website == "job58":
            headers.update({
                "Referer": "https://www.58.com/",
                "Origin": "https://www.58.com"
            })
        
        return headers
    
    async def random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    def get_viewport_size(self) -> Dict[str, int]:
        """获取随机视口大小"""
        viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1440, "height": 900},
            {"width": 1536, "height": 864},
            {"width": 1280, "height": 720},
        ]
        return random.choice(viewports)
    
    def should_use_proxy(self, proxy_list: List[str], use_proxy: bool = False) -> tuple:
        """判断是否使用代理并返回代理URL"""
        if not use_proxy or not proxy_list:
            return False, None
        
        # 随机选择代理
        proxy_url = random.choice(proxy_list)
        return True, proxy_url
    
    def get_js_stealth_code(self) -> List[str]:
        """获取隐身JavaScript代码"""
        stealth_scripts = [
            # 隐藏webdriver属性
            """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            """,
            
            # 修改plugins
            """
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            """,
            
            # 修改languages
            """
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            """,
            
            # 隐藏自动化标识
            """
            window.chrome = {
                runtime: {},
            };
            """,
            
            # 修改权限查询
            """
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            """
        ]
        
        return stealth_scripts


# 创建全局反反爬虫管理器实例
anti_detection = AntiDetectionManager()
