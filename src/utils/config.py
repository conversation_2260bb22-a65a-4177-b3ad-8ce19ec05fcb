"""
配置管理模块
Configuration management module
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List
from pydantic import BaseModel, Field


class CrawlerSettings(BaseModel):
    """爬虫基础设置"""
    max_concurrent_requests: int = Field(default=3, description="最大并发请求数")
    request_delay: float = Field(default=2.0, description="请求间隔(秒)")
    timeout: int = Field(default=30, description="请求超时时间(秒)")
    retry_attempts: int = Field(default=3, description="重试次数")
    headless: bool = Field(default=True, description="是否使用无头浏览器")


class AntiDetectionSettings(BaseModel):
    """反反爬虫设置"""
    rotate_user_agents: bool = Field(default=True, description="是否轮换用户代理")
    use_proxy: bool = Field(default=False, description="是否使用代理")
    proxy_list: List[str] = Field(default_factory=list, description="代理列表")
    random_delay_range: tuple = Field(default=(1, 3), description="随机延迟范围")


class DataStorageSettings(BaseModel):
    """数据存储设置"""
    output_format: str = Field(default="json", description="输出格式: json, csv, database")
    output_directory: str = Field(default="data/output", description="输出目录")
    database_url: str = Field(default="sqlite:///data/jobs.db", description="数据库连接URL")
    enable_deduplication: bool = Field(default=True, description="是否启用去重")


class WebsiteConfig(BaseModel):
    """网站配置"""
    name: str = Field(description="网站名称")
    base_url: str = Field(description="基础URL")
    search_url: str = Field(description="搜索URL模板")
    enabled: bool = Field(default=True, description="是否启用")
    extraction_schema_path: str = Field(description="提取模式文件路径")


class Config:
    """主配置类"""
    
    def __init__(self, config_path: str = "config/crawler_config.yaml"):
        self.config_path = Path(config_path)
        self.config_data = self._load_config()
        
        # 初始化各个配置部分
        self.crawler = CrawlerSettings(**self.config_data.get("crawler", {}))
        self.anti_detection = AntiDetectionSettings(**self.config_data.get("anti_detection", {}))
        self.data_storage = DataStorageSettings(**self.config_data.get("data_storage", {}))
        
        # 网站配置
        self.websites = {}
        for site_name, site_config in self.config_data.get("websites", {}).items():
            self.websites[site_name] = WebsiteConfig(name=site_name, **site_config)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            return self._create_default_config()
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        default_config = {
            "crawler": {
                "max_concurrent_requests": 3,
                "request_delay": 2.0,
                "timeout": 30,
                "retry_attempts": 3,
                "headless": True
            },
            "anti_detection": {
                "rotate_user_agents": True,
                "use_proxy": False,
                "proxy_list": [],
                "random_delay_range": [1, 3]
            },
            "data_storage": {
                "output_format": "json",
                "output_directory": "data/output",
                "database_url": "sqlite:///data/jobs.db",
                "enable_deduplication": True
            },
            "websites": {
                "liepin": {
                    "base_url": "https://www.liepin.com",
                    "search_url": "https://www.liepin.com/zhaopin/?key=计算机科学与技术",
                    "enabled": True,
                    "extraction_schema_path": "config/extraction_schemas/liepin_schema.json"
                },
                "job51": {
                    "base_url": "https://www.51job.com",
                    "search_url": "https://search.51job.com/list/000000,000000,0000,00,9,99,计算机科学与技术,2,1.html",
                    "enabled": True,
                    "extraction_schema_path": "config/extraction_schemas/job51_schema.json"
                },
                "job58": {
                    "base_url": "https://www.58.com",
                    "search_url": "https://www.58.com/sou/?key=计算机科学与技术",
                    "enabled": True,
                    "extraction_schema_path": "config/extraction_schemas/job58_schema.json"
                }
            }
        }
        
        # 确保配置目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存默认配置
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        return default_config
    
    def get_enabled_websites(self) -> List[WebsiteConfig]:
        """获取启用的网站配置"""
        return [config for config in self.websites.values() if config.enabled]
    
    def get_website_config(self, website_name: str) -> WebsiteConfig:
        """获取指定网站配置"""
        if website_name not in self.websites:
            raise ValueError(f"网站配置不存在: {website_name}")
        return self.websites[website_name]


# 全局配置实例
config = Config()
