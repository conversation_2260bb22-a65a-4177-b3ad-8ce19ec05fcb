"""
日志记录模块
Logging module
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{log_color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)


class CrawlerLogger:
    """爬虫日志记录器"""
    
    def __init__(self, name: str = "crawler", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        # 文件处理器 - 详细日志
        today = datetime.now().strftime('%Y-%m-%d')
        file_handler = logging.FileHandler(
            self.log_dir / f"{self.name}_{today}.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        # 错误日志文件处理器
        error_handler = logging.FileHandler(
            self.log_dir / f"{self.name}_error_{today}.log",
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        
        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, **kwargs)
    
    def log_crawl_start(self, website: str, url: str):
        """记录爬取开始"""
        self.info(f"开始爬取 {website}: {url}")
    
    def log_crawl_success(self, website: str, url: str, items_count: int):
        """记录爬取成功"""
        self.info(f"成功爬取 {website}: {url}, 获取 {items_count} 条数据")
    
    def log_crawl_error(self, website: str, url: str, error: str):
        """记录爬取错误"""
        self.error(f"爬取失败 {website}: {url}, 错误: {error}")
    
    def log_data_processing(self, action: str, count: int):
        """记录数据处理"""
        self.info(f"数据处理 - {action}: {count} 条记录")
    
    def log_duplicate_removed(self, count: int):
        """记录去重结果"""
        self.info(f"去重完成，移除 {count} 条重复记录")
    
    def log_data_saved(self, format_type: str, file_path: str, count: int):
        """记录数据保存"""
        self.info(f"数据已保存为 {format_type} 格式: {file_path}, 共 {count} 条记录")


# 创建全局日志记录器实例
logger = CrawlerLogger()

# 为不同模块创建专用日志记录器
crawler_logger = CrawlerLogger("crawler")
data_logger = CrawlerLogger("data_processing")
storage_logger = CrawlerLogger("storage")
